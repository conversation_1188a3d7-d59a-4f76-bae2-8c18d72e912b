"use client";
import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEmployeesForPeriod } from "@/hooks/tanstack-query/useEmployeesForPeriod";
import { useReopenPayslipsMutation } from "@/hooks/tanstack-query/usePayslipActions";
import { usePayPeriodsQuery } from "@/hooks/tanstack-query/usePayPeriods";

type ReopenPayslipsModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialPeriodId?: string; // Optional initial period selection
};

export default function ReopenPayslipsModal({
  open,
  onOpenChange,
  initialPeriodId,
}: ReopenPayslipsModalProps) {
  // Get all pay periods to allow selection
  const { data: payPeriods = [] } = usePayPeriodsQuery();

  // State for selected period
  const [selectedPeriodId, setSelectedPeriodId] = useState<string>(
    initialPeriodId || "",
  );

  // Get employees for the selected period
  const rawEmployees = useEmployeesForPeriod(selectedPeriodId);
  const employees = useMemo(() => rawEmployees, [rawEmployees]);

  // Get mutation for the selected period
  const { mutate } = useReopenPayslipsMutation(selectedPeriodId);
  const [selected, setSelected] = useState<string[]>([]);

  // Debug logging
  console.log("ReopenPayslipsModal selectedPeriodId:", selectedPeriodId);
  console.log("ReopenPayslipsModal employees:", employees);
  useEffect(() => {
    if (open) {
      setSelected(employees.map((e) => e.id));
    }
  }, [open]);
  const toggle = (id: string) =>
    setSelected((prev) =>
      prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id],
    );
  const selectAll = () => setSelected(employees.map((e) => e.id));
  const selectNone = () => setSelected([]);

  // Filter periods to show only those with closed payslips
  const periodsWithClosedPayslips = payPeriods.filter((period) => {
    // For now, show all periods. In the future, we could filter to only show periods with closed payslips
    return true;
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Reopen Payslips</DialogTitle>
        </DialogHeader>

        {/* Period Selection */}
        <div className="mb-4">
          <label className="mb-2 block text-sm font-medium">
            Select Pay Period:
          </label>
          <Select value={selectedPeriodId} onValueChange={setSelectedPeriodId}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a pay period to reopen..." />
            </SelectTrigger>
            <SelectContent>
              {periodsWithClosedPayslips.map((period) => (
                <SelectItem key={period.id} value={period.id}>
                  Period {period.period_number} - {period.period_end} (
                  {period.type})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="mb-2 flex justify-end space-x-2">
          <Button size="sm" variant="outline" onClick={selectAll}>
            Select All
          </Button>
          <Button size="sm" variant="outline" onClick={selectNone}>
            Select None
          </Button>
        </div>
        <div className="max-h-64 space-y-1 overflow-y-auto">
          {employees.map((emp) => (
            <div key={emp.id} className="flex items-center space-x-2">
              <Checkbox
                checked={selected.includes(emp.id)}
                onCheckedChange={() => toggle(emp.id)}
              />
              <span>{emp.name}</span>
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button variant="ghost" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={() => {
              console.log(
                "ReopenPayslipsModal: Reopening payslips for employees:",
                selected,
              );
              console.log("ReopenPayslipsModal: Period ID:", selectedPeriodId);
              mutate(selected);
              onOpenChange(false);
            }}
            disabled={selected.length === 0}
          >
            OK
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
